import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { LotusIcon, LotusSimpleIcon } from "@/components/ui/lotus-icon";
import {
  Activity,
  ArrowRight,
  Award,
  Check,
  CheckCircle,
  ChevronRight,
  Clock,
  Heart,
  HeartPulse,
  MessageCircle,
  Phone,
  ShieldCheck,
  Sparkles,
  Star,
  Target,
  TrendingUp,
  Trophy,
  Users,
  Waves,
  Zap,
} from "lucide-react";
import NavBar from "@/components/homepage/NavBar";
import Hero from "@/components/homepage/Hero";
import Programs from "@/components/homepage/Programs";
import WhyUs from "@/components/homepage/WhyUs";
import Pricing from "@/components/homepage/Pricing";
import Testimonials from "@/components/homepage/Testimonials";
import FAQ from "@/components/homepage/FAQ";
import Location from "@/components/homepage/Location";
import Footer from "@/components/homepage/Footer";

export default function Page() {
  // Mock iletişim
  const phone = "+905320000000";
  const whatsapp = "https://wa.me/905320000000";
  const email = "<EMAIL>";
  const address = "Bağdat Caddesi No:123, Kadıköy, İstanbul";
  const instagram = "https://instagram.com/locafitstudio";

  // Yeni ana sayfa tasarımı — eski içerik yerine bu bölüm render edilir
  return (
    <>
      <a
        href="#ana-icerik"
        className="sr-only focus:not-sr-only focus:absolute focus:p-2 focus:m-2 focus:bg-white dark:focus:bg-black focus:rounded"
      >
        Icerige atla
      </a>
      <main id="ana-icerik" className="min-h-[100svh] bg-gradient-modern">
        <NavBar phone={phone} />
        <Hero whatsappUrl={whatsapp} />
        <Programs />
        <WhyUs />
        <Pricing />
        <Testimonials />
        <FAQ />
        <Location
          address={address}
          phone={phone}
          email={email}
          whatsappUrl={whatsapp}
        />
        <Footer />
      </main>

      <a
        href={whatsapp}
        target="_blank"
        rel="noopener noreferrer"
        aria-label="WhatsApp ile hizli iletisim"
        className="fixed bottom-6 right-6 z-50"
      >
        <Button
          variant="gradient"
          size="xl"
          className="rounded-full shadow-soft"
        >
          <MessageCircle className="mr-2" />
          WhatsApp
        </Button>
      </a>
    </>
  );
}
