"use client";

import { useMemo, useState, useCallback, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import {
  ChevronLeft,
  ChevronRight,
  GripVertical,
  ArrowLeft,
  Calendar,
  AlertCircle,
  Loader2,
  WifiOff,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  WORKING_HOURS,
  isTimeSlotInPast,
  type TimeSlotCapacity,
} from "@/components/appointments/shared/appointment-utils";
import {
  checkTimeSlotCapacity,
  getAllScheduledSessions,
} from "@/lib/actions/database";
import { bookSingleAppointment } from "@/lib/actions/appointments";
import type {
  FlexDashboardStats,
  FlexPackageGroup,
  ServerFlexPackageSessionWithDetails,
} from "@/components/appointments/FlexDashboard";
import Link from "next/link";
import { ErrorBoundary } from "@/components/appointments/shared/ErrorBoundary";
import { BookingProgress } from "@/components/appointments/shared/LoadingStates";

// Enhanced interfaces with better type safety
interface InteractivePlannerProps {
  initialFlexPackages: FlexPackageGroup[];
  initialBookedDates: string[];
  onBookingSuccess?: (booking: BookingSuccessData) => void;
  onBookingError?: (error: BookingError) => void;
  maxWeeksAhead?: number;
  maxWeeksBehind?: number;
}

interface BookingSuccessData {
  memberPackageId: string;
  sessionDate: string;
  sessionTime: string;
  packageName: string;
}

interface BookingError {
  code: string;
  message: string;
  details?: string[];
}

interface SlotState {
  loading: boolean;
  capacity?: TimeSlotCapacity;
  error?: string;
}

interface DragState {
  isDragging: boolean;
  draggedPackageId: string | null;
  draggedPackageName: string | null;
}

interface ComponentErrorState {
  hasError: boolean;
  errorMessage: string;
  errorCode?: string;
  retryCount: number;
  lastErrorTime?: number;
}

interface NetworkState {
  isOnline: boolean;
  isSlowConnection: boolean;
}

type BookingStep = "idle" | "validating" | "checking" | "booking" | "updating";

// Type guards for better type safety
const isValidTimeSlot = (date: string, time: string): boolean => {
  if (!date || !time) return false;
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  const timeRegex = /^\d{2}:\d{2}$/;
  return dateRegex.test(date) && timeRegex.test(time);
};

const isValidPackageId = (id: unknown): id is string => {
  return typeof id === "string" && id.length > 0;
};

function InteractivePlannerCore({
  initialFlexPackages,
  initialBookedDates,
  onBookingSuccess,
  onBookingError,
  maxWeeksAhead = 8,
  maxWeeksBehind = 0,
}: InteractivePlannerProps) {
  // Enhanced state management with better type safety
  const [flexPackages, setFlexPackages] =
    useState<FlexPackageGroup[]>(initialFlexPackages);
  const [bookedDates, setBookedDates] = useState<Set<string>>(
    new Set(initialBookedDates)
  );
  const [slotState, setSlotState] = useState<Record<string, SlotState>>({});
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedPackageId: null,
    draggedPackageName: null,
  });
  const [weekOffset, setWeekOffset] = useState(0);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingStep, setBookingStep] = useState<BookingStep>("idle");
  const [isLoadingCapacities, setIsLoadingCapacities] = useState(false);
  const [errorState, setErrorState] = useState<ComponentErrorState>({
    hasError: false,
    errorMessage: "",
    retryCount: 0,
  });
  const [networkState, setNetworkState] = useState<NetworkState>({
    isOnline: true, // Default to online to avoid hydration mismatch
    isSlowConnection: false,
  });

  // Refs for performance optimization
  const abortController = useRef<AbortController | null>(null);
  const retryTimeouts = useRef<Record<string, NodeJS.Timeout>>({});

  // Memoized stats calculation with better performance
  const stats: FlexDashboardStats = useMemo(() => {
    return flexPackages.reduce(
      (acc, pkg) => {
        acc.totalSessions += pkg.totalSessions;
        acc.usedSessions += pkg.usedSessions;
        acc.remainingSessions += pkg.remainingSessions;

        const scheduledCount = pkg.sessions.filter(
          (s) => s.status === "scheduled"
        ).length;
        const completedCount = pkg.sessions.filter(
          (s) => s.status === "completed"
        ).length;

        acc.scheduledSessions += scheduledCount;
        acc.completedSessions += completedCount;
        return acc;
      },
      {
        totalSessions: 0,
        usedSessions: 0,
        remainingSessions: 0,
        scheduledSessions: 0,
        completedSessions: 0,
      }
    );
  }, [flexPackages]);

  // Memoized week days calculation with validation
  const weekDays = useMemo(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0);
    start.setDate(start.getDate() + weekOffset * 7);
    return Array.from({ length: 7 }, (_, i) => {
      const d = new Date(start);
      d.setDate(start.getDate() + i);
      return d.toISOString().split("T")[0];
    });
  }, [weekOffset]);

  // Network monitoring and cleanup
  useEffect(() => {
    const handleOnline = () => {
      setNetworkState((prev) => ({ ...prev, isOnline: true }));
      setErrorState((prev) => ({ ...prev, hasError: false }));
    };

    const handleOffline = () => {
      setNetworkState((prev) => ({ ...prev, isOnline: false }));
      setErrorState((prev) => ({
        ...prev,
        hasError: true,
        errorMessage: "İnternet bağlantısı kesildi",
        errorCode: "NETWORK_ERROR",
      }));
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Copy ref values to avoid stale closure
    const currentRetryTimeouts = retryTimeouts.current;
    const currentAbortController = abortController.current;

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);

      if (currentAbortController) {
        currentAbortController.abort();
      }

      // Clear retry timeouts
      Object.values(currentRetryTimeouts).forEach(clearTimeout);
    };
  }, []);

  // Error handling utilities
  const handleError = useCallback(
    (error: Error, context: string) => {
      console.error(`Error in ${context}:`, error);

      const errorMessage = error.message || "Bilinmeyen bir hata oluştu";
      const errorCode = error.name || "UNKNOWN_ERROR";

      setErrorState((prev) => ({
        hasError: true,
        errorMessage,
        errorCode,
        retryCount: prev.retryCount,
        lastErrorTime: Date.now(),
      }));

      // Show user-friendly error message
      if (!networkState.isOnline) {
        toast.error("İnternet bağlantınızı kontrol edin");
      } else if (errorCode === "AbortError") {
        // Don't show error for aborted requests
        return;
      } else {
        toast.error(errorMessage);
      }
    },
    [networkState.isOnline]
  );

  // Retry mechanism with exponential backoff
  const retryWithBackoff = useCallback(
    (operation: () => Promise<void>, key: string, maxRetries: number = 3) => {
      const currentRetryCount = errorState.retryCount;

      if (currentRetryCount >= maxRetries) {
        toast.error("Maksimum deneme sayısına ulaşıldı");
        return;
      }

      const delay = Math.min(1000 * Math.pow(2, currentRetryCount), 10000);

      retryTimeouts.current[key] = setTimeout(async () => {
        try {
          setErrorState((prev) => ({
            ...prev,
            retryCount: prev.retryCount + 1,
          }));
          await operation();
          setErrorState((prev) => ({
            ...prev,
            hasError: false,
            retryCount: 0,
          }));
        } catch (error) {
          handleError(error as Error, `retry-${key}`);
        }
      }, delay);
    },
    [errorState.retryCount, handleError]
  );

  // Fetch all scheduled sessions for capacity display
  const fetchAllCapacities = useCallback(async () => {
    try {
      setIsLoadingCapacities(true);
      setSlotState({});
      const capacities = await getAllScheduledSessions();

      // Convert to our slot state format
      const newSlotState: Record<string, SlotState> = {};
      Object.entries(capacities).forEach(([key, capacity]) => {
        newSlotState[key] = {
          loading: false,
          capacity,
        };
      });

      setSlotState(newSlotState);
    } catch (error) {
      const err = error as Error;
      handleError(err, "fetch-all-capacities");
    } finally {
      setIsLoadingCapacities(false);
    }
  }, [handleError]);

  // Load capacities on component mount and week change
  useEffect(() => {
    fetchAllCapacities();
  }, [fetchAllCapacities, weekOffset]);

  // Initialize network state after hydration to avoid mismatch
  useEffect(() => {
    setNetworkState((prev) => ({
      ...prev,
      isOnline: navigator.onLine,
    }));

    const handleOnline = () =>
      setNetworkState((prev) => ({ ...prev, isOnline: true }));
    const handleOffline = () =>
      setNetworkState((prev) => ({ ...prev, isOnline: false }));

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Enhanced drag and drop handler with better error handling
  const handleDropToSlot = useCallback(
    async (
      e: React.DragEvent<HTMLButtonElement>,
      date: string,
      time: string
    ) => {
      e.preventDefault();

      if (isBooking) {
        toast.error("Lütfen mevcut işlemin tamamlanmasını bekleyin");
        return;
      }

      const memberPackageId = e.dataTransfer.getData("memberPackageId");
      if (!isValidPackageId(memberPackageId)) {
        toast.error("Geçersiz paket seçimi");
        return;
      }

      if (!isValidTimeSlot(date, time)) {
        toast.error("Geçersiz zaman dilimi");
        return;
      }

      if (isTimeSlotInPast(date, time)) {
        toast.error("Geçmiş saatlere randevu alınamaz");
        return;
      }

      if (bookedDates.has(date)) {
        toast.error("Aynı güne yalnızca bir randevu alabilirsiniz");
        return;
      }

      setIsBooking(true);
      setBookingStep("validating");
      setDragState((prev) => ({ ...prev, isDragging: false }));

      try {
        // Step 1: Validate and check capacity
        setBookingStep("checking");
        const key = `${date}-${time}`;
        const capacity = slotState[key]?.capacity;

        // If we don't have capacity info, check it now
        if (!capacity) {
          const freshCapacity = await checkTimeSlotCapacity(date, time);
          if (!freshCapacity.available) {
            toast.error("Kapasite dolu");
            return;
          }
        } else if (!capacity.available) {
          toast.error("Kapasite dolu");
          return;
        }

        // Step 2: Create booking
        setBookingStep("booking");
        const formData = new FormData();
        formData.append("memberPackageId", memberPackageId);
        formData.append("sessionDate", date);
        formData.append("sessionTime", time);

        const result = await bookSingleAppointment(formData);

        if (!result.success) {
          const error: BookingError = {
            code: "BOOKING_FAILED",
            message: result.message,
            details: result.errors,
          };

          onBookingError?.(error);
          toast.error(result.message);

          if (result.errors?.length) {
            result.errors.forEach((errorMsg) => toast.error(errorMsg));
          }
          return;
        }

        // Find package name for success callback
        const packageName =
          flexPackages.find((pkg) => pkg.memberPackage.id === memberPackageId)
            ?.packageName || "Bilinmeyen Paket";

        const successData: BookingSuccessData = {
          memberPackageId,
          sessionDate: date,
          sessionTime: time,
          packageName,
        };

        // Step 3: Update local state
        setBookingStep("updating");

        onBookingSuccess?.(successData);
        toast.success("Randevu başarıyla oluşturuldu");

        // Optimistic local updates with better type safety
        setBookedDates((prev) => new Set([...Array.from(prev), date]));

        setFlexPackages((prev) => {
          return prev.map((pkg) => {
            if (pkg.memberPackage.id !== memberPackageId) return pkg;

            const newSession: ServerFlexPackageSessionWithDetails = {
              id: `temp_${Date.now()}_${Math.random().toString(36).slice(2)}`,
              member_id: "",
              member_package_id: memberPackageId,
              session_date: date,
              session_time: time,
              status: "scheduled",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              packageName: pkg.packageName,
            };

            return {
              ...pkg,
              sessions: [...pkg.sessions, newSession],
              usedSessions: pkg.usedSessions,
              remainingSessions: Math.max(0, pkg.remainingSessions - 1),
            };
          });
        });

        // Refresh all capacities after successful booking
        await fetchAllCapacities();
      } catch (error) {
        const err = error as Error;
        const errorMessage = err.message || "Bilinmeyen hata oluştu";

        handleError(err, "booking");

        const bookingError: BookingError = {
          code: err.name || "UNEXPECTED_ERROR",
          message: errorMessage,
        };

        onBookingError?.(bookingError);

        // Retry for network errors
        if (!networkState.isOnline || err.name === "NetworkError") {
          retryWithBackoff(
            () => handleDropToSlot(e, date, time),
            `booking-${date}-${time}`
          );
        }
      } finally {
        setIsBooking(false);
        setBookingStep("idle");
      }
    },
    [
      isBooking,
      bookedDates,
      slotState,
      flexPackages,
      onBookingSuccess,
      onBookingError,
      handleError,
      networkState.isOnline,
      retryWithBackoff,
      fetchAllCapacities,
    ]
  );

  // Enhanced navigation handlers with validation
  const handleWeekNavigation = useCallback(
    (direction: "prev" | "next") => {
      setWeekOffset((current) => {
        const newOffset = direction === "prev" ? current - 1 : current + 1;

        // Validate bounds
        if (newOffset < -maxWeeksBehind || newOffset > maxWeeksAhead) {
          toast.error(
            direction === "prev"
              ? "Daha geçmiş tarihlere gidemezsiniz"
              : "Daha ileri tarihlere gidemezsiniz"
          );
          return current;
        }

        return newOffset;
      });
    },
    [maxWeeksBehind, maxWeeksAhead]
  );

  // Current week display
  const currentWeekDisplay = useMemo(() => {
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0);
    startDate.setDate(startDate.getDate() + weekOffset * 7);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    return `${startDate.toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "short",
    })} - ${endDate.toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "short",
      year: "numeric",
    })}`;
  }, [weekOffset]);

  return (
    <div className="space-y-4 py-3">
      {/* Enhanced Header with better accessibility */}
      <header className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Link
              href="/appointments"
              className="inline-flex items-center gap-1 h-8 px-3 text-sm hover:bg-muted rounded-md transition-colors"
              aria-label="Randevular sayfasına geri dön"
            >
              <ArrowLeft className="w-4 h-4" />
              Geri Dön
            </Link>
            {dragState.isDragging && (
              <Badge variant="secondary" className="animate-pulse">
                <GripVertical className="w-3 h-3 mr-1" />
                {dragState.draggedPackageName} sürükleniyor
              </Badge>
            )}
            {!networkState.isOnline && (
              <Badge variant="destructive" className="animate-pulse">
                <WifiOff className="w-3 h-3 mr-1" />
                Çevrimdışı
              </Badge>
            )}
            {isBooking && bookingStep !== "idle" && (
              <BookingProgress step={bookingStep} />
            )}
          </div>
          <div>
            <h1 className="text-xl font-bold">Akıllı Planlayıcı</h1>
            <p className="text-sm text-muted-foreground">
              Paket haklarınızı takvim üzerinde sürükle-bırak ile planlayın
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          {/* Week display and stats */}
          <div className="text-right">
            <div className="text-sm font-medium">{currentWeekDisplay}</div>
            <div className="text-xs text-muted-foreground">
              {stats.remainingSessions} kullanılabilir seans
            </div>
            {errorState.hasError && (
              <div className="text-xs text-destructive mt-1">
                {errorState.errorMessage}
              </div>
            )}
          </div>

          {/* Navigation controls */}
          <div
            className="flex items-center gap-1"
            role="group"
            aria-label="Hafta navigasyonu"
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleWeekNavigation("prev")}
              disabled={weekOffset <= -maxWeeksBehind}
              className="h-8 w-8"
              aria-label={`Önceki hafta (${
                weekOffset <= -maxWeeksBehind ? "devre dışı" : "aktif"
              })`}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setWeekOffset(0)}
              className="h-8 px-2 text-xs"
              aria-label="Bu haftaya dön"
            >
              Bugün
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleWeekNavigation("next")}
              disabled={weekOffset >= maxWeeksAhead}
              className="h-8 w-8"
              aria-label={`Sonraki hafta (${
                weekOffset >= maxWeeksAhead ? "devre dışı" : "aktif"
              })`}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main calendar area with enhanced accessibility */}
      <main className="pb-32" role="main" aria-label="Haftalık randevu takvimi">
        {/* Weekly board */}
        <section aria-label="Takvim görünümü">
          <div
            className="overflow-x-auto -mx-1 px-1"
            role="region"
            aria-label="Kaydırılabilir takvim"
          >
            <div className="min-w-[900px] relative">
              {isLoadingCapacities && (
                <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center rounded-lg">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Kapasite bilgileri yükleniyor...
                  </div>
                </div>
              )}
              <div
                className="grid gap-2"
                style={{
                  gridTemplateColumns: `120px repeat(${weekDays.length}, minmax(120px,1fr))`,
                  gridAutoRows: "minmax(48px,auto)",
                }}
                role="grid"
                aria-label="Haftalık randevu takvimi"
                aria-rowcount={WORKING_HOURS.length + 1}
                aria-colcount={weekDays.length + 1}
              >
                {/* Header row: empty corner + dates */}
                <div
                  className="px-2 py-2 text-xs sticky top-0 left-0 z-30 rounded-md border border-primary/30 bg-gradient-to-r from-primary/15 to-pink-200/20 text-foreground"
                  role="columnheader"
                  aria-label="Saat ve tarih başlığı"
                >
                  <span className="sr-only">Saat/Tarih</span>
                  <Calendar className="w-4 h-4 mx-auto" aria-hidden="true" />
                </div>
                {weekDays.map((date) => {
                  const isToday =
                    new Date(date).toDateString() === new Date().toDateString();
                  const dayDisabled = bookedDates.has(date);
                  const dow = new Date(date).getDay();
                  const isWeekend = dow === 0 || dow === 6;
                  return (
                    <div
                      key={`hdr-${date}`}
                      className={cn(
                        "px-2 py-2 text-xs font-medium sticky top-0 z-20 rounded-md border bg-gradient-to-b from-background/90 to-primary/10 backdrop-blur supports-[backdrop-filter]:bg-background/80 border-primary/20 text-foreground",
                        isToday && "ring-1 ring-primary/40 shadow-sm",
                        isWeekend && "bg-amber-50/60 dark:bg-amber-900/10",
                        dayDisabled && "opacity-80"
                      )}
                    >
                      <div className="font-semibold">
                        {new Date(date).toLocaleDateString("tr-TR", {
                          weekday: "short",
                        })}
                      </div>
                      <div className="text-[10px] text-muted-foreground">
                        {new Date(date).toLocaleDateString("tr-TR", {
                          day: "2-digit",
                          month: "2-digit",
                        })}
                      </div>
                      {dayDisabled && (
                        <Badge variant="outline" className="mt-1 text-[10px]">
                          Planlı
                        </Badge>
                      )}
                    </div>
                  );
                })}

                {/* Rows: each hour label + date cells */}
                {WORKING_HOURS.map((time) => (
                  <div key={`row-${time}`} className="contents">
                    <div className="px-3 py-2 border rounded-md sticky left-0 z-20 bg-primary/10 border-primary/20 text-primary">
                      <div className="text-sm font-semibold">{time}</div>
                    </div>

                    {weekDays.map((date) => {
                      const key = `${date}-${time}`;
                      const entry = slotState[key];
                      const capacity = entry?.capacity;
                      const isFull = capacity ? !capacity.available : false;
                      const isNearFull = capacity
                        ? capacity.available &&
                          capacity.currentCount >=
                            Math.max(1, capacity.maxCapacity - 1)
                        : false;

                      // Check if user has an appointment at this date/time
                      const hasUserAppointment = flexPackages.some((pkg) =>
                        pkg.sessions.some((session) => {
                          const sessionTimeFormatted =
                            session.session_time.slice(0, 5); // Remove seconds
                          return (
                            session.status === "scheduled" &&
                            session.session_date === date &&
                            sessionTimeFormatted === time
                          );
                        })
                      );

                      const isToday =
                        new Date(date).toDateString() ===
                        new Date().toDateString();
                      const dow = new Date(date).getDay();
                      const isWeekend = dow === 0 || dow === 6;
                      const dayDisabled = bookedDates.has(date);
                      const disabled =
                        dayDisabled || isTimeSlotInPast(date, time) || isFull;
                      const hasError = entry?.error;

                      return (
                        <motion.button
                          key={key}
                          onDragOver={(e) => {
                            if (!disabled) {
                              e.preventDefault();
                              e.dataTransfer.dropEffect = "move";
                            }
                          }}
                          onDragEnter={(e) => {
                            if (!disabled) {
                              e.preventDefault();
                              e.currentTarget.classList.add(
                                "ring-2",
                                "ring-primary",
                                "bg-primary/5"
                              );
                            }
                          }}
                          onDragLeave={(e) => {
                            if (!disabled) {
                              e.currentTarget.classList.remove(
                                "ring-2",
                                "ring-primary",
                                "bg-primary/5"
                              );
                            }
                          }}
                          onDrop={(e) => {
                            if (!disabled) handleDropToSlot(e, date, time);
                          }}
                          disabled={disabled || isBooking}
                          title={`${new Date(date).toLocaleDateString(
                            "tr-TR"
                          )} ${time}${hasError ? ` - Hata: ${hasError}` : ""}`}
                          className={cn(
                            "relative text-left px-3 py-2 rounded-lg border text-xs transition-all duration-200",
                            disabled
                              ? "opacity-50 cursor-not-allowed bg-muted/40 pointer-events-none"
                              : "hover:bg-primary/10 hover:border-primary/30 hover:shadow-sm",
                            hasUserAppointment
                              ? "bg-primary/20 border-primary/60 ring-2 ring-primary/30"
                              : isFull
                              ? "bg-destructive/10 border-destructive/40"
                              : isNearFull
                              ? "bg-amber-500/10 border-amber-500/30"
                              : "border-border/50 hover:border-primary/20",
                            isToday && "ring-1 ring-primary/20 bg-primary/5",
                            isWeekend &&
                              !disabled &&
                              "bg-amber-50/40 dark:bg-amber-900/10",
                            hasError &&
                              "border-destructive/30 bg-destructive/5",
                            dragState.isDragging &&
                              !disabled &&
                              "ring-2 ring-primary/30 bg-primary/5"
                          )}
                          whileHover={!disabled ? { scale: 1.02 } : {}}
                          whileTap={!disabled ? { scale: 0.98 } : {}}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              {hasUserAppointment && (
                                <div className="text-[10px] font-semibold text-primary">
                                  Randevum
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              {entry?.loading && (
                                <Loader2 className="w-3 h-3 animate-spin text-muted-foreground" />
                              )}
                              {hasError && (
                                <AlertCircle className="w-3 h-3 text-destructive" />
                              )}
                              {capacity && !hasError && (
                                <>
                                  <span
                                    className={cn(
                                      "w-2 h-2 rounded-full",
                                      hasUserAppointment
                                        ? "bg-primary"
                                        : isFull
                                        ? "bg-destructive"
                                        : isNearFull
                                        ? "bg-amber-500"
                                        : "bg-emerald-500"
                                    )}
                                  />
                                  <span className="text-[10px] text-muted-foreground font-medium">
                                    {capacity.currentCount}/
                                    {capacity.maxCapacity}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                          {isBooking && (
                            <div className="absolute inset-0 bg-primary/10 rounded-lg flex items-center justify-center">
                              <Loader2 className="w-4 h-4 animate-spin text-primary" />
                            </div>
                          )}
                        </motion.button>
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Enhanced Fixed bottom packages dock with better accessibility */}
      <aside
        className="fixed bottom-0 inset-x-0 z-40 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/80"
        role="complementary"
        aria-label="Paket seçimi ve sürükleme alanı"
      >
        <div className="mx-auto max-w-7xl px-3 py-2">
          <div className="mb-2 flex items-center justify-between">
            <h2 className="text-sm font-medium text-muted-foreground">
              Kullanılabilir Paketler
            </h2>
            {dragState.isDragging && (
              <div className="text-xs text-muted-foreground animate-pulse">
                Takvimde bir zaman dilimine bırakın
              </div>
            )}
          </div>
          <div
            className="flex items-stretch gap-3 overflow-x-auto"
            role="list"
            aria-label="Sürüklenebilir paket listesi"
          >
            {flexPackages.map((pkg) => {
              const total = pkg.totalSessions;
              const planned = pkg.sessions.filter(
                (s) => s.status !== "completed"
              ).length;
              const progressPct =
                total > 0 ? Math.round((planned / total) * 100) : 0;
              const availableTokens = Math.max(0, pkg.remainingSessions);
              const isCompleted = pkg.memberPackage.status === "completed";
              return (
                <div
                  key={pkg.memberPackage.id}
                  className="group relative min-w-[320px] rounded-xl p-3 border border-primary/20 bg-gradient-to-br from-background via-primary/10 to-pink-100/20 dark:to-primary/5 shadow-sm hover:shadow-md transition"
                  role="listitem"
                  aria-label={`${pkg.packageName} paketi, ${availableTokens} kullanılabilir seans`}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 min-w-[180px]">
                      <div className="text-sm font-semibold tracking-tight truncate">
                        {pkg.packageName}
                      </div>
                      <Badge
                        variant="outline"
                        className="text-xs px-2 py-0.5 rounded-full border-primary/30 text-primary"
                      >
                        {isCompleted ? "Tamamlandı" : `${availableTokens} hak`}
                      </Badge>
                    </div>

                    <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                      <div
                        className={cn(
                          "h-full rounded-full transition-all",
                          isCompleted
                            ? "bg-gradient-to-r from-emerald-500 via-emerald-400 to-emerald-600"
                            : "bg-gradient-to-r from-primary via-fuchsia-500 to-amber-500"
                        )}
                        style={{ width: `${progressPct}%` }}
                      />
                    </div>

                    {!isCompleted && (
                      <motion.div
                        whileHover={
                          availableTokens > 0 && !isBooking
                            ? { scale: 1.05 }
                            : {}
                        }
                        whileTap={
                          availableTokens > 0 && !isBooking
                            ? { scale: 0.95 }
                            : {}
                        }
                      >
                        <button
                          draggable={availableTokens > 0 && !isBooking}
                          onDragStart={(
                            e: React.DragEvent<HTMLButtonElement>
                          ) => {
                            if (availableTokens > 0 && !isBooking) {
                              e.dataTransfer.setData(
                                "memberPackageId",
                                pkg.memberPackage.id
                              );
                              e.dataTransfer.effectAllowed = "move";

                              // Create custom drag image
                              const dragImage = document.createElement("div");
                              dragImage.innerHTML = `
                                <div style="
                                  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary)/0.8));
                                  color: white;
                                  padding: 8px 12px;
                                  border-radius: 8px;
                                  font-size: 12px;
                                  font-weight: 500;
                                  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                                  white-space: nowrap;
                                ">
                                  📅 ${pkg.packageName} - 1 seans
                                </div>
                              `;
                              dragImage.style.position = "absolute";
                              dragImage.style.top = "-1000px";
                              document.body.appendChild(dragImage);
                              e.dataTransfer.setDragImage(dragImage, 0, 0);

                              // Clean up drag image after a short delay
                              setTimeout(() => {
                                document.body.removeChild(dragImage);
                              }, 0);

                              setDragState({
                                isDragging: true,
                                draggedPackageId: pkg.memberPackage.id,
                                draggedPackageName: pkg.packageName,
                              });
                            }
                          }}
                          onDragEnd={() => {
                            setDragState({
                              isDragging: false,
                              draggedPackageId: null,
                              draggedPackageName: null,
                            });
                          }}
                          className={cn(
                            "px-2.5 py-1.5 text-xs rounded-md inline-flex items-center gap-1 select-none transition-all duration-200",
                            availableTokens > 0 && !isBooking
                              ? "cursor-grab active:cursor-grabbing bg-gradient-to-r from-primary to-pink-500 hover:opacity-90 text-white shadow-sm"
                              : "cursor-not-allowed opacity-50 bg-muted text-muted-foreground"
                          )}
                          title={
                            isBooking
                              ? "Randevu işlemi devam ediyor..."
                              : availableTokens > 0
                              ? "Bu paketten bir seansı takvime sürükleyin"
                              : "Bu pakette kullanılabilir seans kalmamış"
                          }
                          disabled={availableTokens === 0 || isBooking}
                          aria-label={`${pkg.packageName} paketinden seans sürükle`}
                        >
                          {isBooking ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <GripVertical className="w-4 h-4" />
                          )}
                          {isBooking ? "İşleniyor..." : "Sürükleyin"}
                        </button>
                      </motion.div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </aside>
    </div>
  );
}

// Enhanced wrapper component with error boundary
export default function InteractivePlanner(props: InteractivePlannerProps) {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error("InteractivePlanner Error:", error, errorInfo);
        // TODO: Send to error tracking service in production
      }}
    >
      <InteractivePlannerCore {...props} />
    </ErrorBoundary>
  );
}
