import { cookies } from "next/headers";
import { unstable_cache } from "next/cache";

import FlexDashboard, {
  FlexDashboardStats,
  FlexPackageGroup,
  ServerFlexPackageSessionWithDetails,
} from "@/components/appointments/FlexDashboard";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getMemberAllPackages,
  getMemberSessions,
} from "@/lib/actions/database";

export default async function AppointmentsPage() {
  const cookieStore = await cookies();
  const phone = cookieStore.get("memberPhone")?.value || null;
  if (!phone) return null;

  // Auto-complete past sessions and invalidate cache
  const completedCount = await autoCompletePastSessions();
  const member = await getMemberFromPhone(phone);
  if (!member) return null;

  // Cache'i bypass et eğer session'lar complete edilmişse
  let allPackages, allSessions;

  if (completedCount > 0) {
    // Session'lar güncellenmişse cache'i bypass et
    console.log(`Bypassing cache due to ${completedCount} completed sessions`);
    [allPackages, allSessions] = await Promise.all([
      getMemberAllPackages(member.id),
      getMemberSessions(member.id),
    ]);
  } else {
    // Normal cache kullan
    const getMemberData = unstable_cache(
      async () => {
        const [allPackages, allSessions] = await Promise.all([
          getMemberAllPackages(member.id),
          getMemberSessions(member.id),
        ]);
        return { allPackages, allSessions };
      },
      ["appointments-data", member.id],
      { tags: ["appointments", `appointments-${member.id}`] }
    );

    const data = await getMemberData();
    allPackages = data.allPackages;
    allSessions = data.allSessions;
  }

  // Debug logging
  console.log("All packages:", allPackages);
  console.log("All sessions:", allSessions);

  const packagesWithSessions: FlexPackageGroup[] = allPackages.map(
    (memberPackage) => {
      const packageSessions = allSessions.filter(
        (session) => session.member_package_id === memberPackage.id
      );

      console.log(`Package ${memberPackage.id} sessions:`, packageSessions);

      return {
        packageName: memberPackage.flex_package?.name || "Esnek Paket",
        sessions: packageSessions as ServerFlexPackageSessionWithDetails[],
        memberPackage,
        totalSessions: memberPackage.total_sessions,
        usedSessions: memberPackage.used_sessions,
        remainingSessions:
          memberPackage.total_sessions - memberPackage.used_sessions,
        expiryDate: memberPackage.expiry_date,
      };
    }
  );

  console.log("Packages with sessions:", packagesWithSessions);

  const initialStats: FlexDashboardStats = packagesWithSessions.reduce(
    (acc, pkg) => {
      acc.totalSessions += pkg.totalSessions;
      acc.usedSessions += pkg.usedSessions;
      acc.remainingSessions += pkg.remainingSessions;
      acc.scheduledSessions += pkg.sessions.filter(
        (s) => s.status === "scheduled"
      ).length;
      acc.completedSessions += pkg.sessions.filter(
        (s) => s.status === "completed"
      ).length;
      return acc;
    },
    {
      totalSessions: 0,
      usedSessions: 0,
      remainingSessions: 0,
      scheduledSessions: 0,
      completedSessions: 0,
      activePackages: 0,
    }
  );

  return (
    <FlexDashboard
      initialFlexPackages={packagesWithSessions}
      initialStats={initialStats}
    />
  );
}
