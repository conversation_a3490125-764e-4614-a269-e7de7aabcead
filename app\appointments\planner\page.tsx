import { cookies } from "next/headers";
import { unstable_cache } from "next/cache";

import InteractivePlanner from "@/components/appointments/InteractivePlanner";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getMemberAllPackages,
  getMemberSessions,
} from "@/lib/actions/database";
import type { MemberFlexPackageWithDetails } from "@/lib/supabase/types";

export default async function PlannerPage() {
  const cookieStore = await cookies();
  const phone = cookieStore.get("memberPhone")?.value || null;
  if (!phone) return null;

  // Mark past sessions completed; this also helps data be fresh
  const completedCount = await autoCompletePastSessions();
  const member = await getMemberFromPhone(phone);
  if (!member) return null;

  let allPackages: MemberFlexPackageWithDetails[] = [];
  let allSessions: any[] = [];

  if (completedCount > 0) {
    [allPackages, allSessions] = await Promise.all([
      getMemberAllPackages(member.id),
      getMemberSessions(member.id),
    ]);
  } else {
    const getData = unstable_cache(
      async () => {
        const [pkgs, sess] = await Promise.all([
          getMemberAllPackages(member.id),
          getMemberSessions(member.id),
        ]);
        return { pkgs, sess };
      },
      ["appointments-planner-data", member.id],
      { tags: ["appointments", `appointments-${member.id}`] }
    );
    const data = await getData();
    allPackages = data.pkgs;
    allSessions = data.sess;
  }

  // Group sessions by member package and compute aggregates
  const packagesWithSessions = allPackages.map((memberPackage) => {
    const packageSessions = allSessions.filter(
      (s) => s.member_package_id === memberPackage.id
    );

    return {
      packageName: memberPackage.flex_package?.name || "Esnek Paket",
      sessions: packageSessions,
      memberPackage,
      totalSessions: memberPackage.total_sessions,
      usedSessions: memberPackage.used_sessions,
      remainingSessions:
        memberPackage.total_sessions - memberPackage.used_sessions,
      expiryDate: memberPackage.expiry_date,
    };
  });

  // Debug logging
  console.log("All sessions:", allSessions);
  console.log("All packages:", allPackages);
  console.log("Packages with sessions:", packagesWithSessions);

  // Booked dates for quick checks in planner (scheduled only)
  const bookedDates = Array.from(
    new Set(
      allSessions
        .filter((s) => s.status === "scheduled")
        .map((s) => s.session_date)
    )
  );

  console.log("Booked dates:", bookedDates);

  return (
    <InteractivePlanner
      initialFlexPackages={packagesWithSessions as any}
      initialBookedDates={bookedDates}
    />
  );
}
